use anyhow::{Result, anyhow};
use ndarray::{Array, Ix2};
use ort::{
    Error,
    session::{Session, builder::GraphOptimizationLevel},
    value::TensorRef,
};
use tokenizers::Tokenizer;

fn main() -> ort::Result<()> {
    // Initialize tracing to receive debug messages from `ort`
    // tracing_subscriber::registry()
    // 	.with(tracing_subscriber::EnvFilter::try_from_default_env().unwrap_or_else(|_| "info,ort=debug".into()))
    // 	.with(tracing_subscriber::fmt::layer())
    // 	.init();

    // Load our model
    let mut session = Session::builder()?
        .with_optimization_level(GraphOptimizationLevel::Level1)?
        .with_intra_threads(1)?
        .commit_from_file("model.onnx")?;

    let tokenizer = Tokenizer::from_file("tokenizer.json").unwrap();
    let query = "What is the capital of France?";
    let passage = "Paris is the capital of France.";

    let encoding = tokenizer.encode((query, passage), true).unwrap();
    let input_ids = encoding.get_ids();
    let attention_mask = encoding.get_attention_mask();
    let token_type_ids = encoding.get_type_ids();

    let input_ids_array = Array::from_shape_vec(
        (1, input_ids.len()),
        input_ids.iter().map(|&x| x as i64).collect(),
    )
    .unwrap(); // Added .unwrap() and semicolon

    let attention_mask_array = Array::from_shape_vec(
        (1, attention_mask.len()),
        attention_mask.iter().map(|&x| x as i64).collect(),
    )
    .unwrap(); // Added .unwrap() and semicolon

    let token_type_ids_array = Array::from_shape_vec(
        (1, token_type_ids.len()),
        token_type_ids.iter().map(|&x| x as i64).collect(),
    )
    .unwrap(); // Added .unwrap() and semicolon

	


let inputs = ort::inputs! {
        "input_ids" => input_ids_array.into(),
        "attention_mask" => attention_mask_array.into(),
        "token_type_ids" => token_type_ids_array.into()
    }
    .unwrap(); // .unwrap() or ? is now used on the result of ort::inputs!

    // Run the model
    let outputs: Vec<Value> = session.run(inputs)?;

    // Access the 'logits' output and use `try_extract_tensor` as per ort v2.
    let output_tensor = outputs[0].try_extract_tensor::<f32>()?;
    let score = output_tensor.view().get(()).unwrap();

    println!("\nQuery: {}", query);
    println!("Passage: {}", passage);
    println!("Reranking Score: {:.4}", score);





    Ok(())
}
